<?php

namespace app\api\controller;

use app\api\logic\OrderInvoiceLogic;
use app\api\logic\OrderLogic;
use app\api\validate\OrderValidate;
use app\common\basics\Api;
use app\common\server\JsonServer;
use app\common\server\WechatMiniExpressSendSyncServer;
use app\common\model\order\Order as OrderModel;
use app\common\enum\OrderEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\PayEnum;


class  Order extends Api
{
    /**
     * @notes 下单
     * @return \think\response\Json
     * @throws \think\Exception
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function submitOrder()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        (new OrderValidate())->goCheck('add', $post);
        $order = OrderLogic::add($post);
        if (false === $order) {
            if(OrderLogic::getError()==1102){
                //非集采用户无法购买集采商品
                return JsonServer::error('',[],1102);
            }
            return JsonServer::error(OrderLogic::getError());
        }
        return JsonServer::success('下单成功!', $order);
    }

    /**
     * @notes 结算页数据
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function settlement()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $result = OrderLogic::settlement($post);
        if($result === false) {
            return JsonServer::error(OrderLogic::getError(), [], 301);
        }
        return JsonServer::success('获取成功', $result);
    }


    /**
     * @notes 订单列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function lists()
    {
        $type = $this->request->get('type', 'all');
        $shop_id = $this->request->get('shop_id', 0);
        $order_list = OrderLogic::getOrderList($this->user_id, $type, $this->page_no, $this->page_size,$shop_id);
        return JsonServer::success('获取成功', $order_list);
    }

    /**
     * @notes 获取订单详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function getOrderDetail()
    {
        $get = $this->request->get();
        (new OrderValidate())->goCheck('detail', $get);
        $detail = OrderLogic::getOrderDetail($get['id']);
        return JsonServer::success('获取成功', $detail);
    }
    
    /**
     * @notes 微信确认收货 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @datetime 2023-09-05 09:51:41
     */
    function wxReceiveDetail()
    {
        return JsonServer::success('获取成功', OrderLogic::wxReceiveDetail(input('order_id/d'), $this->user_id));
    }

    /**
     * @notes 取消订单（立即更新状态，异步处理退款）
     * @return array|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function cancel()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }

        try {
            // 1. 先同步更新订单状态
            $result = $this->updateOrderStatusSync($order_id, $this->user_id);
            if (!$result['success']) {
                return JsonServer::error($result['message']);
            }

            // 2. 如果订单已支付，异步处理退款
            if ($result['need_refund']) {
                \think\facade\Queue::push('app\common\job\OrderRefundJob', [
                    'order_id' => $order_id,
                    'user_id' => $this->user_id,
                    'refund_amount' => $result['refund_amount']
                ], 'orderCancel');

                // 3. 异步处理后续操作（回退库存、返回优惠券等）
                \think\facade\Queue::push('app\common\job\AfterCancelOrderJob', [
                    'type' => OrderLogEnum::TYPE_USER,
                    'channel' => OrderLogEnum::USER_CANCEL_ORDER,
                    'order_id' => $order_id,
                    'handle_id' => $this->user_id,
                ], 'orderCancel');

                return JsonServer::success('订单已取消，退款正在处理中...');
            } else {
                // 4. 未支付订单，直接处理后续操作
                \think\facade\Queue::push('app\common\job\AfterCancelOrderJob', [
                    'type' => OrderLogEnum::TYPE_USER,
                    'channel' => OrderLogEnum::USER_CANCEL_ORDER,
                    'order_id' => $order_id,
                    'handle_id' => $this->user_id,
                ], 'orderCancel');

                return JsonServer::success('订单取消成功');
            }

        } catch (\Exception $e) {
            // 异常时回退到原始同步处理
            \think\facade\Log::error('订单取消处理失败，回退到同步处理: ' . $e->getMessage());
            return OrderLogic::cancel($order_id, $this->user_id);
        }
    }

    /**
     * @notes 同步更新订单状态
     * @param int $order_id
     * @param int $user_id
     * @return array
     */
    private function updateOrderStatusSync($order_id, $user_id)
    {
        $time = time();
        $order = OrderModel::with(['orderGoods'])->where(['del' => 0, 'user_id' => $user_id, 'id' => $order_id])->find();

        if (!$order || (int)$order['order_status'] > OrderEnum::ORDER_STATUS_DELIVERY) {
            return ['success' => false, 'message' => '很抱歉!订单无法取消'];
        }

        // 开启事务
        \think\facade\Db::startTrans();
        try {
            // 更新订单状态为已取消
            $order->order_status = OrderEnum::ORDER_STATUS_DOWN;
            $order->update_time = $time;
            $order->cancel_time = $time;
            $order->save();

            // 记录订单日志
            \app\common\logic\OrderLogLogic::record(
                OrderLogEnum::TYPE_USER,
                OrderLogEnum::USER_CANCEL_ORDER,
                $order_id,
                $user_id,
                OrderLogEnum::USER_CANCEL_ORDER
            );

            \think\facade\Db::commit();

            // 判断是否需要退款
            $need_refund = ($order['pay_status'] == PayEnum::ISPAID);
            $refund_amount = $need_refund ? $order['order_amount'] : 0;

            return [
                'success' => true,
                'need_refund' => $need_refund,
                'refund_amount' => $refund_amount
            ];

        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * @notes 确认收货
     * @return array|\think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function confirm()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }
        return OrderLogic::confirm($order_id, $this->user_id);
    }

    /**
     * @notes 删除订单
     * @return array|\think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function del()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }
        return OrderLogic::del($order_id, $this->user_id);
    }

    /**
     * @notes 订单支付结果页面数据
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function pay_result()
    {
        $id = $this->request->get('id');
        $from = $this->request->get('from');//标识：trade：父订单，order：子订单
        $result = OrderLogic::pay_result($id,$from);
        if ($result !== false) {
            return JsonServer::success('', $result);
        } else {
            return JsonServer::error('参数错误');
        }
    }

    /**
     * @notes 获取支付方式
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function getPayWay()
    {
        $params = $this->request->get();
        if(!isset($params['from']) || !isset($params['order_id'])) {
            return JsonServer::error('参数缺失');
        }
        $pay_way = OrderLogic::getPayWay($this->user_id, $this->client, $params);
        return JsonServer::success('', $pay_way);
    }

    /**
     * @notes 物流查询
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function orderTraces()
    {
        $order_id = $this->request->get('id');
        $tips = '参数错误';
        if ($order_id) {
            $traces = OrderLogic::orderTraces($order_id, $this->user_id);


            if ($traces) {
                return JsonServer::success('获取成功', $traces);
            }
            $tips = '暂无物流信息';
        }
        return JsonServer::error($tips);
    }

    /**
     * @notes PC获取支付状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/29 3:52 下午
     */
    public function getPayStatus()
    {
        $id = $this->request->get('id');
        $from = $this->request->get('from');//标识：trade：父订单，order：子订单
        $result = OrderLogic::getPayStatus($id,$from);
        if ($result !== false) {
            return JsonServer::success('', $result);
        } else {
            return JsonServer::error('参数错误');
        }
    }



    /**
     * @notes 发票详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2022/4/12 9:25
     */
    public function invoice()
    {
        $id = $this->request->get('id');
        $result = OrderInvoiceLogic::getInvoiceDetailByOrderId($id);
        return JsonServer::success('获取成功', $result);
    }
    
    /**
     * @notes 微信同步发货 查询
     * @return \think\response\Json
     * <AUTHOR>
     * @datetime 2023-09-07 15:27:17
     */
    function wechatSyncCheck()
    {
        $id     = $this->request->get('id');
        
        $order  = \app\common\model\order\Order::where('id', $id)->where('user_id', $this->user_id)->findOrEmpty();
        
        $result = WechatMiniExpressSendSyncServer::wechatSyncCheck($order);

        if (! $result) {
            return JsonServer::error('未开通电商类目,无法同步收货');
        }
    
        return JsonServer::success('成功', $result);
    }

    /**
     * @notes 根据已选规格获取剩余可用规格及库存
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/18
     */
    public function getGoodsStock()
    {
        $params = $this->request->get();
        $goods_id = $params['goods_id'] ?? 0;
        // 支持两种格式: spec_names=红色,S 或 spec_names[]=红色&spec_names[]=S
        $spec_values = $params['spec_values'] ?? '';

        if (empty($goods_id) || empty($spec_values)) {
            return JsonServer::error('参数错误: goods_id 和 spec_values 不能为空');
        }

        if (is_string($spec_values)) {
            $spec_values = array_filter(explode(',', $spec_values));
        }

        $result = \app\api\logic\GoodsLogic::getAvailableSpecStock($goods_id, $spec_values);

        if ($result === false) {
            return JsonServer::error(\app\api\logic\GoodsLogic::getError() ?: '查询失败');
        }

        return JsonServer::success('获取成功', $result);
    }

    /**
     * @notes 修改订单地址
     * @return \think\response\Json
     * <AUTHOR> Assistant
     * @date 2025/01/26
     */
    public function editAddress()
    {
        $post = $this->request->post();
        $result = OrderLogic::editAddress($post, $this->user_id);
        if ($result === false) {
            return JsonServer::error(OrderLogic::getError() ?: '修改地址失败');
        }

        return JsonServer::success('操作成功');
    }
}
